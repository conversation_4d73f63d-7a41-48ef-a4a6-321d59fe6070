<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('calendar_events', function (Blueprint $table) {
            $table->integer('minimum_notice_value')->nullable()->after('minimum_notice');
            $table->enum('minimum_notice_unit', ['minutes', 'hours', 'days', 'months'])->default('minutes')->after('minimum_notice_value');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('calendar_events', function (Blueprint $table) {
            $table->dropColumn(['minimum_notice_value', 'minimum_notice_unit']);
        });
    }
};
