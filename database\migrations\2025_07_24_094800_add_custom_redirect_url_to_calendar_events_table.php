<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('calendar_events', function (Blueprint $table) {
            if (!Schema::hasColumn('calendar_events', 'custom_redirect_url')) {
                $table->string('custom_redirect_url', 500)->nullable()->after('locations_data')
                      ->comment('Custom URL to redirect users after booking confirmation');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('calendar_events', function (Blueprint $table) {
            if (Schema::hasColumn('calendar_events', 'custom_redirect_url')) {
                $table->dropColumn('custom_redirect_url');
            }
        });
    }
};
