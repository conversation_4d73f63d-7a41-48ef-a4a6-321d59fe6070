<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meeting Confirmed</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .email-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        
        .success-badge {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 10px 20px;
            display: inline-block;
            margin-top: 15px;
            font-weight: 600;
        }
        
        .content {
            padding: 30px 20px;
        }
        
        .event-title {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .detail-row {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-icon {
            width: 40px;
            height: 40px;
            background: #f0fdf4;
            color: #22c55e;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 18px;
        }
        
        .detail-content {
            flex: 1;
        }
        
        .detail-label {
            font-size: 14px;
            color: #6b7280;
            font-weight: 500;
            margin-bottom: 3px;
        }
        
        .detail-value {
            font-size: 16px;
            color: #1f2937;
            font-weight: 600;
        }
        
        .calendar-section {
            background: #f9fafb;
            padding: 25px 20px;
            text-align: center;
        }
        
        .calendar-section h3 {
            margin: 0 0 20px 0;
            font-size: 20px;
            color: #1f2937;
        }
        
        .calendar-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .calendar-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 14px;
            color: white;
            transition: all 0.2s ease;
        }
        
        .calendar-btn.google {
            background: #4285f4;
        }
        
        .calendar-btn.yahoo {
            background: #7b68ee;
        }
        
        .calendar-btn.outlook {
            background: #0078d4;
        }
        
        .footer {
            padding: 20px;
            text-align: center;
            background: #f9fafb;
            border-top: 1px solid #e5e7eb;
        }
        
        .footer p {
            margin: 0;
            color: #6b7280;
            font-size: 14px;
        }
        
        .company-name {
            font-weight: 600;
            color: #22c55e;
        }
        
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .header {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .content {
                padding: 20px 15px;
            }
            
            .calendar-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .calendar-btn {
                width: 100%;
                max-width: 250px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>🎉 Meeting Confirmed!</h1>
            <div class="success-badge">
                ✅ You are scheduled with <?php echo e(config('app.name', 'OMX Digital')); ?>

            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="event-title"><?php echo e($event->title); ?></div>
            
            <!-- Date & Time -->
            <div class="detail-row">
                <div class="detail-icon">📅</div>
                <div class="detail-content">
                    <div class="detail-label">Date & Time</div>
                    <div class="detail-value">
                        <?php echo e(\Carbon\Carbon::parse($booking->date . ' ' . $booking->time)->format('g:i A - ')); ?><?php echo e(\Carbon\Carbon::parse($booking->date . ' ' . $booking->time)->addMinutes($event->duration ?? 30)->format('g:i A, l, F j, Y')); ?>

                    </div>
                </div>
            </div>

            <!-- Location -->
            <div class="detail-row">
                <div class="detail-icon">
                    <?php if($booking->selected_location): ?>
                        <?php $location = $booking->selected_location; ?>
                        <?php if($location['type'] === 'zoom'): ?>
                            🎥
                        <?php elseif($location['type'] === 'in_person'): ?>
                            📍
                        <?php elseif($location['type'] === 'phone'): ?>
                            📞
                        <?php elseif($location['type'] === 'meet'): ?>
                            🎯
                        <?php elseif($location['type'] === 'skype'): ?>
                            💻
                        <?php else: ?>
                            🌐
                        <?php endif; ?>
                    <?php else: ?>
                        📍
                    <?php endif; ?>
                </div>
                <div class="detail-content">
                    <div class="detail-label">Location</div>
                    <div class="detail-value">
                        <?php if($booking->selected_location): ?>
                            <?php echo e($booking->selected_location['display']); ?>

                            <?php if($booking->selected_location['value']): ?>
                                <br><small style="color: #6b7280;"><?php echo e($booking->selected_location['value']); ?></small>
                            <?php endif; ?>
                        <?php elseif($event->location === 'in_person'): ?>
                            In-person meeting
                            <?php if($event->physical_address): ?>
                                <br><small style="color: #6b7280;"><?php echo e($event->physical_address); ?></small>
                            <?php endif; ?>
                        <?php elseif($event->location === 'zoom'): ?>
                            Zoom meeting
                        <?php elseif($event->location === 'meet'): ?>
                            Google Meet
                        <?php elseif($event->location === 'skype'): ?>
                            Skype meeting
                        <?php else: ?>
                            <?php echo e(ucfirst($event->location ?? 'Online')); ?> meeting
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Attendee -->
            <div class="detail-row">
                <div class="detail-icon">👤</div>
                <div class="detail-content">
                    <div class="detail-label">Attendee</div>
                    <div class="detail-value"><?php echo e($booking->name); ?></div>
                </div>
            </div>

            <!-- Email -->
            <div class="detail-row">
                <div class="detail-icon">✉️</div>
                <div class="detail-content">
                    <div class="detail-label">Email</div>
                    <div class="detail-value"><?php echo e($booking->email); ?></div>
                </div>
            </div>

            <?php if($booking->phone): ?>
            <!-- Phone -->
            <div class="detail-row">
                <div class="detail-icon">📱</div>
                <div class="detail-content">
                    <div class="detail-label">Phone</div>
                    <div class="detail-value"><?php echo e($booking->phone); ?></div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Calendar Section -->
        <div class="calendar-section">
            <h3>Add to Your Calendar</h3>
            <div class="calendar-buttons">
                <a href="#" class="calendar-btn google" id="google-calendar">
                    📅 Add to Google
                </a>
                <a href="#" class="calendar-btn yahoo" id="yahoo-calendar">
                    📅 Add to Yahoo
                </a>
                <a href="#" class="calendar-btn outlook" id="outlook-calendar">
                    📅 Add to Outlook
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Thank you for booking with <span class="company-name"><?php echo e(config('app.name', 'OMX Digital')); ?></span></p>
            <p>If you need to reschedule or cancel, please contact us as soon as possible.</p>
        </div>
    </div>

    <script>
        // Generate calendar links
        document.addEventListener('DOMContentLoaded', function() {
            const booking = <?php echo json_encode($booking, 15, 512) ?>;
            const event = <?php echo json_encode($event, 15, 512) ?>;
            
            const startDate = new Date(booking.date + 'T' + booking.time);
            const endDate = new Date(startDate.getTime() + (event.duration || 30) * 60000);
            
            const title = encodeURIComponent(event.title);
            const description = encodeURIComponent(event.description || 'Meeting with ' + '<?php echo e(config("app.name")); ?>');
            
            let location = '';
            if (booking.selected_location) {
                location = encodeURIComponent(booking.selected_location.display + (booking.selected_location.value ? ' - ' + booking.selected_location.value : ''));
            } else if (event.location === 'in_person') {
                location = encodeURIComponent(event.physical_address || 'In-person meeting');
            } else {
                location = encodeURIComponent(event.meet_link || 'Online meeting');
            }
            
            const startDateStr = startDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
            const endDateStr = endDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
            
            // Google Calendar
            const googleUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${startDateStr}/${endDateStr}&details=${description}&location=${location}`;
            document.getElementById('google-calendar').href = googleUrl;
            
            // Yahoo Calendar
            const yahooUrl = `https://calendar.yahoo.com/?v=60&view=d&type=20&title=${title}&st=${startDateStr}&dur=${event.duration || 30}&desc=${description}&in_loc=${location}`;
            document.getElementById('yahoo-calendar').href = yahooUrl;
            
            // Outlook Calendar
            const outlookUrl = `https://outlook.live.com/calendar/0/deeplink/compose?subject=${title}&startdt=${startDateStr}&enddt=${endDateStr}&body=${description}&location=${location}`;
            document.getElementById('outlook-calendar').href = outlookUrl;
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/emails/booking-confirmation.blade.php ENDPATH**/ ?>