<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meeting Confirmed! - <?php echo e(config('app.name', 'OMX Digital')); ?></title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #22c55e;
            --primary-dark: #16a34a;
            --success-light: #dcfce7;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --border-light: #e5e7eb;
            --bg-light: #f9fafb;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .confirmation-container {
            max-width: 600px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .confirmation-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            border: 1px solid var(--border-light);
        }

        .header-section {
            text-align: center;
            padding: 3rem 2rem 2rem;
            background: linear-gradient(135deg, var(--success-light) 0%, white 100%);
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: 0 10px 30px rgba(34, 197, 94, 0.3);
        }

        .success-icon i {
            color: white;
            font-size: 2rem;
        }

        .confirmation-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .confirmation-subtitle {
            background: var(--success-light);
            color: var(--primary-dark);
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            display: inline-flex;
            align-items: center;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .confirmation-subtitle i {
            margin-right: 0.5rem;
            color: var(--primary-color);
        }

        .email-notice {
            color: var(--text-secondary);
            font-size: 0.95rem;
            margin-bottom: 0;
        }

        .booking-details {
            padding: 2rem;
        }

        .event-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 2rem;
            text-align: center;
        }

        .detail-item {
            display: flex;
            align-items: flex-start;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-light);
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-icon {
            width: 40px;
            height: 40px;
            background: var(--bg-light);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .detail-icon i {
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .detail-content {
            flex: 1;
        }

        .detail-label {
            font-size: 0.85rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .detail-value {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .location-badge {
            display: inline-flex;
            align-items: center;
            background: var(--success-light);
            color: var(--primary-dark);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
            margin-top: 0.5rem;
            text-decoration: none;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .location-badge i {
            margin-right: 0.5rem;
            color: var(--primary-color);
        }

        .location-badge-clickable {
            cursor: pointer;
            background: linear-gradient(135deg, var(--success-light) 0%, #e8f5e8 100%);
            border: 2px solid var(--primary-color);
            box-shadow: 0 2px 8px rgba(34, 197, 94, 0.15);
        }

        .location-badge-clickable:hover {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(34, 197, 94, 0.3);
        }

        .location-badge-clickable:hover i {
            color: white;
        }

        .location-badge-clickable:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);
        }

        .location-address {
            font-size: 0.85rem;
            color: var(--text-secondary);
            padding: 0.5rem 0;
        }

        /* Meeting link notification */
        .meeting-link-notice {
            background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
            border: 1px solid #0ea5e9;
            border-radius: 12px;
            padding: 1rem;
            margin-top: 1rem;
            text-align: center;
        }

        .meeting-link-notice i {
            color: #0ea5e9;
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .meeting-link-notice .notice-title {
            font-weight: 600;
            color: #0c4a6e;
            margin-bottom: 0.25rem;
        }

        .meeting-link-notice .notice-text {
            font-size: 0.9rem;
            color: #075985;
        }

        /* Pulse animation for clickable badges */
        .location-badge-clickable::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-radius: 27px;
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
        }

        .location-badge-clickable {
            position: relative;
            z-index: 1;
        }

        .location-badge-clickable:hover::before {
            opacity: 0.2;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.2;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.1;
            }
            100% {
                transform: scale(1);
                opacity: 0.2;
            }
        }

        .calendar-actions {
            padding: 2rem;
            background: var(--bg-light);
            text-align: center;
        }

        .calendar-actions h5 {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        .calendar-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .calendar-btn {
            display: inline-flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            font-size: 0.9rem;
        }

        .calendar-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .btn-google {
            background: #4285f4;
            color: white;
        }

        .btn-google:hover {
            background: #3367d6;
            color: white;
        }

        .btn-yahoo {
            background: #7b68ee;
            color: white;
        }

        .btn-yahoo:hover {
            background: #6a5acd;
            color: white;
        }

        .btn-outlook {
            background: #0078d4;
            color: white;
        }

        .btn-outlook:hover {
            background: #106ebe;
            color: white;
        }

        .footer-section {
            text-align: center;
            padding: 2rem;
            background: white;
            border-top: 1px solid var(--border-light);
        }

        .company-logo {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .footer-text {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .confirmation-container {
                margin: 1rem auto;
                padding: 0 0.5rem;
            }

            .header-section {
                padding: 2rem 1rem 1.5rem;
            }

            .confirmation-title {
                font-size: 1.5rem;
            }

            .booking-details {
                padding: 1.5rem;
            }

            .calendar-buttons {
                flex-direction: column;
                align-items: center;
            }

            .calendar-btn {
                width: 100%;
                max-width: 250px;
                justify-content: center;
            }

            .detail-item {
                flex-direction: column;
                text-align: center;
            }

            .detail-icon {
                margin: 0 auto 0.5rem;
            }
        }

        /* Animation */
        .confirmation-card {
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-icon {
            animation: bounceIn 0.8s ease-out 0.2s both;
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="confirmation-container">
        <div class="confirmation-card">
            <!-- Header Section -->
            <div class="header-section">
                <div class="success-icon">
                    <i class="fas fa-check"></i>
                </div>
                <h1 class="confirmation-title">Meeting Confirmed!</h1>
                <div class="confirmation-subtitle">
                    <i class="fas fa-calendar-check"></i>
                    You are scheduled with <?php echo e(config('app.name', 'OMX Digital')); ?>

                </div>
                <p class="email-notice">A Calendar Invitation has been sent to your email address</p>
            </div>

            <!-- Booking Details -->
            <div class="booking-details">
                <h2 class="event-title"><?php echo e($booking->event->title ?? 'Meeting'); ?></h2>
                
                <div class="detail-item">
                    <div class="detail-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-label">Attendee</div>
                        <div class="detail-value"><?php echo e($booking->name); ?></div>
                    </div>
                </div>

                <div class="detail-item">
                    <div class="detail-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-label">Date & Time</div>
                        <div class="detail-value">
                            <?php echo e(\Carbon\Carbon::parse($booking->date . ' ' . $booking->time)->format('g:i A - g:i A, l, F j, Y')); ?>

                        </div>
                    </div>
                </div>

                <div class="detail-item">
                    <div class="detail-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-label">Time Zone</div>
                        <div class="detail-value"><?php echo e($timezone ?? 'Asia/Calcutta'); ?></div>
                    </div>
                </div>

                <div class="detail-item">
                    <div class="detail-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-label">Location</div>
                        <div class="detail-value">
                            <?php if($booking->selected_location): ?>
                                <?php
                                    $location = is_array($booking->selected_location) ? $booking->selected_location : json_decode($booking->selected_location, true);
                                    $isClickable = in_array($location['type'], ['zoom', 'meet', 'skype']) && !empty($location['value']);
                                    $phoneNumber = $location['type'] === 'phone' && !empty($location['value']) ? $location['value'] : null;
                                ?>

                                <?php if($isClickable): ?>
                                    <a href="<?php echo e($location['value']); ?>" target="_blank" class="location-badge location-badge-clickable" title="Click to join meeting">
                                        <?php if($location['type'] === 'zoom'): ?>
                                            <i class="fas fa-video"></i>
                                        <?php elseif($location['type'] === 'meet'): ?>
                                            <i class="fab fa-google"></i>
                                        <?php elseif($location['type'] === 'skype'): ?>
                                            <i class="fab fa-skype"></i>
                                        <?php endif; ?>
                                        <?php echo e($location['display'] ?? 'Meeting Location'); ?>

                                        <i class="fas fa-external-link-alt ms-2" style="font-size: 0.8rem;"></i>
                                    </a>
                                <?php elseif($phoneNumber): ?>
                                    <a href="tel:<?php echo e($phoneNumber); ?>" class="location-badge location-badge-clickable" title="Click to call">
                                        <i class="fas fa-phone"></i>
                                        <?php echo e($location['display'] ?? 'Phone Call'); ?>

                                        <i class="fas fa-phone ms-2" style="font-size: 0.8rem;"></i>
                                    </a>
                                <?php else: ?>
                                    <div class="location-badge">
                                        <?php if($location['type'] === 'in_person'): ?>
                                            <i class="fas fa-map-marker-alt"></i>
                                        <?php elseif($location['type'] === 'phone'): ?>
                                            <i class="fas fa-phone"></i>
                                        <?php else: ?>
                                            <i class="fas fa-globe"></i>
                                        <?php endif; ?>
                                        <?php echo e($location['display'] ?? 'Meeting Location'); ?>

                                    </div>
                                <?php endif; ?>

                                <?php if(!empty($location['value']) && $location['type'] === 'in_person'): ?>
                                    <div class="location-address mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            <?php echo e($location['value']); ?>

                                        </small>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="location-badge">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <?php echo e($booking->event->location ?? 'To be determined'); ?>

                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <?php if($booking->selected_location): ?>
                    <?php
                        $location = is_array($booking->selected_location) ? $booking->selected_location : json_decode($booking->selected_location, true);
                        $isClickable = in_array($location['type'], ['zoom', 'meet', 'skype']) && !empty($location['value']);
                        $phoneNumber = $location['type'] === 'phone' && !empty($location['value']);
                    ?>

                    <?php if($isClickable): ?>
                        <div class="meeting-link-notice">
                            <i class="fas fa-info-circle"></i>
                            <div class="notice-title">Meeting Link Available</div>
                            <div class="notice-text">Click on the <?php echo e($location['display']); ?> badge above to join your meeting directly</div>
                        </div>
                    <?php elseif($phoneNumber): ?>
                        <div class="meeting-link-notice">
                            <i class="fas fa-phone"></i>
                            <div class="notice-title">Phone Number Available</div>
                            <div class="notice-text">Click on the phone badge above to dial the number directly</div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>

            <!-- Calendar Actions -->
            <div class="calendar-actions">
                <h5>Add to Calendar</h5>
                <div class="calendar-buttons">
                    <a href="#" class="calendar-btn btn-google" id="googleCalendar">
                        <i class="fab fa-google me-2"></i>
                        Add to Google
                    </a>
                    <a href="#" class="calendar-btn btn-yahoo" id="yahooCalendar">
                        <i class="fab fa-yahoo me-2"></i>
                        Add to Yahoo Mail
                    </a>
                    <a href="#" class="calendar-btn btn-outlook" id="outlookCalendar">
                        <i class="fas fa-calendar me-2"></i>
                        Add to Outlook
                    </a>
                </div>
            </div>

            <!-- Footer -->
            <div class="footer-section">
                <div class="company-logo"><?php echo e(config('app.name', 'OMX Digital')); ?></div>
                <p class="footer-text">Thank you for booking with us. We look forward to meeting you!</p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Calendar integration functionality
        document.addEventListener('DOMContentLoaded', function() {
            const bookingData = {
                title: <?php echo json_encode($booking->event->title ?? 'Meeting', 15, 512) ?>,
                startDate: <?php echo json_encode($booking->date, 15, 512) ?>,
                startTime: <?php echo json_encode($booking->time, 15, 512) ?>,
                duration: <?php echo json_encode($booking->event->duration ?? 60, 15, 512) ?>,
                location: <?php echo json_encode($booking->selected_location ? ($booking->selected_location['display'] ?? 'Meeting') : ($booking->event->location ?? 'Meeting'), 15, 512) ?>,
                description: <?php echo json_encode($booking->event->description ?? 'Scheduled meeting', 15, 512) ?>
            };

            // Calculate end time
            const startDateTime = new Date(bookingData.startDate + 'T' + bookingData.startTime);
            const endDateTime = new Date(startDateTime.getTime() + (bookingData.duration * 60000));
            
            // Format dates for calendar URLs
            const formatDate = (date) => {
                return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
            };

            const startFormatted = formatDate(startDateTime);
            const endFormatted = formatDate(endDateTime);

            // Google Calendar URL
            document.getElementById('googleCalendar').href = 
                `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(bookingData.title)}&dates=${startFormatted}/${endFormatted}&details=${encodeURIComponent(bookingData.description)}&location=${encodeURIComponent(bookingData.location)}`;

            // Yahoo Calendar URL
            document.getElementById('yahooCalendar').href = 
                `https://calendar.yahoo.com/?v=60&view=d&type=20&title=${encodeURIComponent(bookingData.title)}&st=${startFormatted}&et=${endFormatted}&desc=${encodeURIComponent(bookingData.description)}&in_loc=${encodeURIComponent(bookingData.location)}`;

            // Outlook Calendar URL
            document.getElementById('outlookCalendar').href = 
                `https://outlook.live.com/calendar/0/deeplink/compose?subject=${encodeURIComponent(bookingData.title)}&startdt=${startFormatted}&enddt=${endFormatted}&body=${encodeURIComponent(bookingData.description)}&location=${encodeURIComponent(bookingData.location)}`;
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/bookings/confirmation.blade.php ENDPATH**/ ?>