[2025-07-24 05:58:00] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-24T05:58:00.277352Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-24 05:58:02] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2042 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T05:58:02.574381Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2228.0,"user_id":84,"entity_id":5,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T05:58:00.346323Z","data":{"id":5,"title":"book","start_date":"2025-07-24 02:26:00","end_date":"2025-07-31 03:26:00","duration":60,"booking_per_slot":1,"minimum_notice":30,"description":"oizh","location":"zoom","meet_link":"https://meet.google.com/xkd-csbv-uyp","physical_address":null,"custom_fields":[{"type":"business_type","label":"Business Type"}],"date_override":null,"created_by":84,"slots_created":64,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2042 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 05:58:04] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2053 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T05:58:04.637331Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2059.0,"user_id":84,"entity_id":5,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T05:58:02.577919Z","data":{"id":5,"title":"book","start_date":"2025-07-24 02:26:00","end_date":"2025-07-31 03:26:00","duration":60,"booking_per_slot":1,"minimum_notice":30,"description":"oizh","location":"zoom","meet_link":"https://meet.google.com/xkd-csbv-uyp","physical_address":null,"custom_fields":[{"type":"business_type","label":"Business Type"}],"date_override":null,"created_by":84,"slots_created":64,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2053 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 05:58:04] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 2 {"timestamp":"2025-07-24T05:58:04.640656Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2042 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2053 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 06:15:39] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-24T06:15:39.868840Z","source":"crm_webhook_system","action":"booking.event_created","user_id":7,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-24 06:15:42] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2035 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T06:15:42.008688Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2131.0,"user_id":7,"entity_id":6,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T06:15:39.877766Z","data":{"id":6,"title":"Mr x","start_date":"2025-07-24 03:43:00","end_date":"2025-07-31 11:47:00","duration":60,"booking_per_slot":1,"minimum_notice":30,"description":"A meeting discussion is a structured conversation between two or more people, often in a professional setting, to address specific topics or make decisions. Effective discussions require active listening, clear communication, and a focus on the meeting's goals.","location":"in_person","meet_link":"https://meet.google.com/hfm-viso-nne","physical_address":"Siliguri West bengal","custom_fields":null,"date_override":null,"created_by":7,"slots_created":64,"triggered_by":{"user_id":7,"email":"<EMAIL>","name":"Yash Agarwal","type":"system admin"}},"user_id":7,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2035 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 06:15:44] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2019 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T06:15:44.031991Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2022.0,"user_id":7,"entity_id":6,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T06:15:42.009683Z","data":{"id":6,"title":"Mr x","start_date":"2025-07-24 03:43:00","end_date":"2025-07-31 11:47:00","duration":60,"booking_per_slot":1,"minimum_notice":30,"description":"A meeting discussion is a structured conversation between two or more people, often in a professional setting, to address specific topics or make decisions. Effective discussions require active listening, clear communication, and a focus on the meeting's goals.","location":"in_person","meet_link":"https://meet.google.com/hfm-viso-nne","physical_address":"Siliguri West bengal","custom_fields":null,"date_override":null,"created_by":7,"slots_created":64,"triggered_by":{"user_id":7,"email":"<EMAIL>","name":"Yash Agarwal","type":"system admin"}},"user_id":7,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2019 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 06:15:44] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 2 {"timestamp":"2025-07-24T06:15:44.033547Z","source":"crm_webhook_system","action":"booking.event_created","user_id":7,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2035 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2019 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 06:18:17] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T06:18:17.254453Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":4,"status":"dispatching"} 
[2025-07-24 06:18:19] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2021 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T06:18:19.441989Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2159.0,"user_id":84,"entity_id":4,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T06:18:17.282584Z","data":{"event_id":5,"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"13:30","custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-24T06:18:17.000000Z","created_at":"2025-07-24T06:18:17.000000Z","id":4,"form_data":{"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"13:30","custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2021 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 06:18:21] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T06:18:21.470594Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2026.0,"user_id":84,"entity_id":4,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T06:18:19.444283Z","data":{"event_id":5,"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"13:30","custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-24T06:18:17.000000Z","created_at":"2025-07-24T06:18:17.000000Z","id":4,"form_data":{"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"13:30","custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 06:18:21] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T06:18:21.472976Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2021 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2022 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 06:20:07] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T06:20:07.577119Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":5,"status":"dispatching"} 
[2025-07-24 06:20:09] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T06:20:09.734783Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2127.0,"user_id":84,"entity_id":5,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T06:20:07.608221Z","data":{"event_id":5,"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-24T06:20:07.000000Z","created_at":"2025-07-24T06:20:07.000000Z","id":5,"form_data":{"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 06:20:11] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2013 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T06:20:11.752726Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2017.0,"user_id":84,"entity_id":5,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T06:20:09.736004Z","data":{"event_id":5,"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-24T06:20:07.000000Z","created_at":"2025-07-24T06:20:07.000000Z","id":5,"form_data":{"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2013 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 06:20:11] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T06:20:11.754899Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2013 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 06:34:03] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T06:34:03.549784Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":6,"status":"dispatching"} 
[2025-07-24 06:34:05] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2041 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T06:34:05.697258Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2125.0,"user_id":84,"entity_id":6,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T06:34:03.572109Z","data":{"event_id":5,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"14:30","selected_location":{"type":"zoom","value":"https://meet.google.com/xkd-csbv-uyp","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-24T06:34:03.000000Z","created_at":"2025-07-24T06:34:03.000000Z","id":6,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"14:30","selected_location":{"type":"zoom","value":"https://meet.google.com/xkd-csbv-uyp","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2041 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 06:34:07] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T06:34:07.729610Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2030.0,"user_id":84,"entity_id":6,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T06:34:05.699312Z","data":{"event_id":5,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"14:30","selected_location":{"type":"zoom","value":"https://meet.google.com/xkd-csbv-uyp","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-24T06:34:03.000000Z","created_at":"2025-07-24T06:34:03.000000Z","id":6,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"14:30","selected_location":{"type":"zoom","value":"https://meet.google.com/xkd-csbv-uyp","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 06:34:07] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T06:34:07.731211Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2041 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 06:47:57] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T06:47:57.083048Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":7,"status":"dispatching"} 
[2025-07-24 06:48:03] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T06:48:03.380308Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":6225.0,"user_id":84,"entity_id":7,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T06:47:57.155624Z","data":{"event_id":5,"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-24","time":"15:30","selected_location":{"type":"skype","value":"https://meet.google.com/xkd-csbv-uyp","display":"Skype"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-24T06:47:56.000000Z","created_at":"2025-07-24T06:47:56.000000Z","id":7,"form_data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-24","time":"15:30","selected_location":{"type":"skype","value":"https://meet.google.com/xkd-csbv-uyp","display":"Skype"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 06:48:05] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T06:48:05.410937Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2028.0,"user_id":84,"entity_id":7,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T06:48:03.382541Z","data":{"event_id":5,"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-24","time":"15:30","selected_location":{"type":"skype","value":"https://meet.google.com/xkd-csbv-uyp","display":"Skype"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-24T06:47:56.000000Z","created_at":"2025-07-24T06:47:56.000000Z","id":7,"form_data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-24","time":"15:30","selected_location":{"type":"skype","value":"https://meet.google.com/xkd-csbv-uyp","display":"Skype"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 06:48:05] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T06:48:05.411880Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 07:14:58] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T07:14:58.104738Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":7,"entity_type":"Booking","entity_id":8,"status":"dispatching"} 
[2025-07-24 07:15:00] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2040 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:15:00.234806Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2111.0,"user_id":7,"entity_id":8,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:14:58.123573Z","data":{"event_id":1,"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","selected_location":null,"custom_fields":["city"],"custom_fields_value":["siliguri"],"updated_at":"2025-07-24T07:14:58.000000Z","created_at":"2025-07-24T07:14:58.000000Z","id":8,"form_data":{"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","selected_location":null,"custom_fields":["city"],"custom_fields_value":["siliguri"],"booking_type":"public"},"triggered_by":{"user_id":7,"email":"<EMAIL>","name":"Yash Agarwal","type":"system admin"}},"user_id":7,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2040 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:15:02] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2019 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:15:02.258630Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2022.0,"user_id":7,"entity_id":8,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:15:00.236533Z","data":{"event_id":1,"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","selected_location":null,"custom_fields":["city"],"custom_fields_value":["siliguri"],"updated_at":"2025-07-24T07:14:58.000000Z","created_at":"2025-07-24T07:14:58.000000Z","id":8,"form_data":{"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","selected_location":null,"custom_fields":["city"],"custom_fields_value":["siliguri"],"booking_type":"public"},"triggered_by":{"user_id":7,"email":"<EMAIL>","name":"Yash Agarwal","type":"system admin"}},"user_id":7,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2019 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:15:02] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T07:15:02.259332Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":7,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2040 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2019 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 07:16:21] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T07:16:21.284368Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":7,"entity_type":"Booking","entity_id":9,"status":"dispatching"} 
[2025-07-24 07:16:23] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:16:23.376438Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2081.0,"user_id":7,"entity_id":9,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:16:21.295296Z","data":{"event_id":1,"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","selected_location":null,"custom_fields":["city"],"custom_fields_value":["siliguri"],"updated_at":"2025-07-24T07:16:21.000000Z","created_at":"2025-07-24T07:16:21.000000Z","id":9,"form_data":{"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","selected_location":null,"custom_fields":["city"],"custom_fields_value":["siliguri"],"booking_type":"public"},"triggered_by":{"user_id":7,"email":"<EMAIL>","name":"Yash Agarwal","type":"system admin"}},"user_id":7,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:16:25] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:16:25.405697Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2029.0,"user_id":7,"entity_id":9,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:16:23.376915Z","data":{"event_id":1,"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","selected_location":null,"custom_fields":["city"],"custom_fields_value":["siliguri"],"updated_at":"2025-07-24T07:16:21.000000Z","created_at":"2025-07-24T07:16:21.000000Z","id":9,"form_data":{"name":"A","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","selected_location":null,"custom_fields":["city"],"custom_fields_value":["siliguri"],"booking_type":"public"},"triggered_by":{"user_id":7,"email":"<EMAIL>","name":"Yash Agarwal","type":"system admin"}},"user_id":7,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:16:25] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T07:16:25.407382Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":7,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 07:26:55] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T07:26:55.776241Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":10,"status":"dispatching"} 
[2025-07-24 07:26:57] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:26:57.886667Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2106.0,"user_id":84,"entity_id":10,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:26:55.781002Z","data":{"event_id":5,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"14:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-24T07:26:49.000000Z","created_at":"2025-07-24T07:26:49.000000Z","id":10,"event":{"id":5,"title":"book","start_date":"2025-07-24T02:26:00.000000Z","end_date":"2025-07-31T03:26:00.000000Z","duration":60,"booking_per_slot":1,"minimum_notice":30,"description":"oizh","location":"zoom","locations_data":"[{\"type\":\"zoom\",\"value\":\"https://meet.google.com/xkd-csbv-uyp\",\"display\":\"Zoom\"},{\"type\":\"phone\",\"value\":\"8617555736\",\"display\":\"Phone call\"},{\"type\":\"meet\",\"value\":\"https://meet.google.com/xkd-csbv-uyp\",\"display\":\"Google Meet\"},{\"type\":\"skype\",\"value\":\"https://meet.google.com/xkd-csbv-uyp\",\"display\":\"Skype\"}]","location_details":null,"meet_link":"https://meet.google.com/xkd-csbv-uyp","physical_address":null,"require_name":true,"require_email":true,"require_phone":false,"custom_field":null,"custom_field_value":null,"weekly_availability":null,"created_by":84,"created_at":"2025-07-24T05:57:58.000000Z","updated_at":"2025-07-24T05:57:58.000000Z","date_override":null,"custom_fields":[{"type":"business_type","label":"Business Type"}]},"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"14:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:26:59] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2038 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:26:59.930215Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2041.0,"user_id":84,"entity_id":10,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:26:57.889436Z","data":{"event_id":5,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"14:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-24T07:26:49.000000Z","created_at":"2025-07-24T07:26:49.000000Z","id":10,"event":{"id":5,"title":"book","start_date":"2025-07-24T02:26:00.000000Z","end_date":"2025-07-31T03:26:00.000000Z","duration":60,"booking_per_slot":1,"minimum_notice":30,"description":"oizh","location":"zoom","locations_data":"[{\"type\":\"zoom\",\"value\":\"https://meet.google.com/xkd-csbv-uyp\",\"display\":\"Zoom\"},{\"type\":\"phone\",\"value\":\"8617555736\",\"display\":\"Phone call\"},{\"type\":\"meet\",\"value\":\"https://meet.google.com/xkd-csbv-uyp\",\"display\":\"Google Meet\"},{\"type\":\"skype\",\"value\":\"https://meet.google.com/xkd-csbv-uyp\",\"display\":\"Skype\"}]","location_details":null,"meet_link":"https://meet.google.com/xkd-csbv-uyp","physical_address":null,"require_name":true,"require_email":true,"require_phone":false,"custom_field":null,"custom_field_value":null,"weekly_availability":null,"created_by":84,"created_at":"2025-07-24T05:57:58.000000Z","updated_at":"2025-07-24T05:57:58.000000Z","date_override":null,"custom_fields":[{"type":"business_type","label":"Business Type"}]},"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"14:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2038 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:26:59] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T07:26:59.930866Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2038 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 07:28:57] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T07:28:57.406179Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":11,"status":"dispatching"} 
[2025-07-24 07:28:59] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2046 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:28:59.505824Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2088.0,"user_id":84,"entity_id":11,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:28:57.417863Z","data":{"event_id":5,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"13:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-24T07:28:57.000000Z","created_at":"2025-07-24T07:28:57.000000Z","id":11,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"13:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2046 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:29:01] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:29:01.541385Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2034.0,"user_id":84,"entity_id":11,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:28:59.507615Z","data":{"event_id":5,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"13:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-24T07:28:57.000000Z","created_at":"2025-07-24T07:28:57.000000Z","id":11,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"13:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:29:01] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T07:29:01.542638Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2046 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 07:29:46] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T07:29:46.790595Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":12,"status":"dispatching"} 
[2025-07-24 07:29:48] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2041 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:29:48.872425Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2074.0,"user_id":84,"entity_id":12,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:29:46.798882Z","data":{"event_id":5,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"14:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-24T07:29:46.000000Z","created_at":"2025-07-24T07:29:46.000000Z","id":12,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"14:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2041 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:29:50] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2046 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:29:50.922806Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2049.0,"user_id":84,"entity_id":12,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:29:48.873880Z","data":{"event_id":5,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"14:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-24T07:29:46.000000Z","created_at":"2025-07-24T07:29:46.000000Z","id":12,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"14:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2046 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:29:50] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T07:29:50.924042Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2041 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2046 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 07:39:28] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T07:39:28.564266Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":13,"status":"dispatching"} 
[2025-07-24 07:39:30] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:39:30.661356Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2087.0,"user_id":84,"entity_id":13,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:39:28.574228Z","data":{"event_id":5,"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-24","time":"15:30","selected_location":{"type":"zoom","value":"https://meet.google.com/xkd-csbv-uyp","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["NA"],"updated_at":"2025-07-24T07:39:28.000000Z","created_at":"2025-07-24T07:39:28.000000Z","id":13,"form_data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-24","time":"15:30","selected_location":{"type":"zoom","value":"https://meet.google.com/xkd-csbv-uyp","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["NA"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:39:32] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2011 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:39:32.676746Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2014.0,"user_id":84,"entity_id":13,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:39:30.662875Z","data":{"event_id":5,"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-24","time":"15:30","selected_location":{"type":"zoom","value":"https://meet.google.com/xkd-csbv-uyp","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["NA"],"updated_at":"2025-07-24T07:39:28.000000Z","created_at":"2025-07-24T07:39:28.000000Z","id":13,"form_data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-24","time":"15:30","selected_location":{"type":"zoom","value":"https://meet.google.com/xkd-csbv-uyp","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["NA"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2011 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:39:32] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T07:39:32.678100Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2011 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 07:49:06] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T07:49:06.369712Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":14,"status":"dispatching"} 
[2025-07-24 07:49:08] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2031 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:49:08.468156Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2082.0,"user_id":84,"entity_id":14,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:49:06.386322Z","data":{"event_id":5,"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-24","time":"15:30","selected_location":{"type":"zoom","value":"https://meet.google.com/xkd-csbv-uyp","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["NA"],"updated_at":"2025-07-24T07:49:06.000000Z","created_at":"2025-07-24T07:49:06.000000Z","id":14,"form_data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-24","time":"15:30","selected_location":{"type":"zoom","value":"https://meet.google.com/xkd-csbv-uyp","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["NA"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2031 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:49:10] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2036 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:49:10.507059Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2038.0,"user_id":84,"entity_id":14,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:49:08.468878Z","data":{"event_id":5,"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-24","time":"15:30","selected_location":{"type":"zoom","value":"https://meet.google.com/xkd-csbv-uyp","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["NA"],"updated_at":"2025-07-24T07:49:06.000000Z","created_at":"2025-07-24T07:49:06.000000Z","id":14,"form_data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-24","time":"15:30","selected_location":{"type":"zoom","value":"https://meet.google.com/xkd-csbv-uyp","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["NA"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2036 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:49:10] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T07:49:10.507605Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2031 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2036 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 07:51:36] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T07:51:36.622848Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":7,"entity_type":"Booking","entity_id":15,"status":"dispatching"} 
[2025-07-24 07:51:38] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2021 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:51:38.709557Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2079.0,"user_id":7,"entity_id":15,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:51:36.630895Z","data":{"event_id":1,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","selected_location":null,"custom_fields":["city"],"custom_fields_value":["string:Siliguri"],"updated_at":"2025-07-24T07:51:36.000000Z","created_at":"2025-07-24T07:51:36.000000Z","id":15,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","selected_location":null,"custom_fields":["city"],"custom_fields_value":["string:Siliguri"],"booking_type":"public"},"triggered_by":{"user_id":7,"email":"<EMAIL>","name":"Yash Agarwal","type":"system admin"}},"user_id":7,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2021 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:51:40] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2040 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T07:51:40.753590Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2043.0,"user_id":7,"entity_id":15,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T07:51:38.710695Z","data":{"event_id":1,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","selected_location":null,"custom_fields":["city"],"custom_fields_value":["string:Siliguri"],"updated_at":"2025-07-24T07:51:36.000000Z","created_at":"2025-07-24T07:51:36.000000Z","id":15,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"12:30","selected_location":null,"custom_fields":["city"],"custom_fields_value":["string:Siliguri"],"booking_type":"public"},"triggered_by":{"user_id":7,"email":"<EMAIL>","name":"Yash Agarwal","type":"system admin"}},"user_id":7,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2040 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 07:51:40] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T07:51:40.754694Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":7,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2021 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2040 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 08:08:23] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-24T08:08:23.108145Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-24 08:08:25] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T08:08:25.187345Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2073.0,"user_id":84,"entity_id":7,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T08:08:23.114056Z","data":{"id":7,"title":"Bot flow Builder","start_date":"2025-07-24 18:26:00","end_date":"2025-07-31 13:29:00","duration":60,"booking_per_slot":1,"minimum_notice":1440,"description":"The Bot Flow Builder is a visual tool that allows you to design, customise, and automate chatbot conversations with a simple drag-and-drop interface. Build dynamic message flows, set conditions, define actions, and create seamless user journeys—all without writing a single line of code.","location":"zoom","meet_link":"https://meet.google.com/inw-yfmf-wxx","physical_address":"Siliguri West Bengal","custom_fields":[{"type":"opportunity_name","label":"Opportunity Name"}],"date_override":null,"created_by":84,"slots_created":56,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 08:08:27] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T08:08:27.220671Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2033.0,"user_id":84,"entity_id":7,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T08:08:25.188124Z","data":{"id":7,"title":"Bot flow Builder","start_date":"2025-07-24 18:26:00","end_date":"2025-07-31 13:29:00","duration":60,"booking_per_slot":1,"minimum_notice":1440,"description":"The Bot Flow Builder is a visual tool that allows you to design, customise, and automate chatbot conversations with a simple drag-and-drop interface. Build dynamic message flows, set conditions, define actions, and create seamless user journeys—all without writing a single line of code.","location":"zoom","meet_link":"https://meet.google.com/inw-yfmf-wxx","physical_address":"Siliguri West Bengal","custom_fields":[{"type":"opportunity_name","label":"Opportunity Name"}],"date_override":null,"created_by":84,"slots_created":56,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 08:08:27] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 2 {"timestamp":"2025-07-24T08:08:27.221146Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 08:11:34] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T08:11:34.192379Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":16,"status":"dispatching"} 
[2025-07-24 08:11:36] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2046 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T08:11:36.279924Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2079.0,"user_id":84,"entity_id":16,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T08:11:34.200566Z","data":{"event_id":7,"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"15:30","selected_location":{"type":"meet","value":"https://meet.google.com/inw-yfmf-wxx","display":"Google Meet"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"updated_at":"2025-07-24T08:11:34.000000Z","created_at":"2025-07-24T08:11:34.000000Z","id":16,"form_data":{"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"15:30","selected_location":{"type":"meet","value":"https://meet.google.com/inw-yfmf-wxx","display":"Google Meet"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2046 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 08:11:38] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2057 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T08:11:38.340016Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2059.0,"user_id":84,"entity_id":16,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T08:11:36.280803Z","data":{"event_id":7,"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"15:30","selected_location":{"type":"meet","value":"https://meet.google.com/inw-yfmf-wxx","display":"Google Meet"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"updated_at":"2025-07-24T08:11:34.000000Z","created_at":"2025-07-24T08:11:34.000000Z","id":16,"form_data":{"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"15:30","selected_location":{"type":"meet","value":"https://meet.google.com/inw-yfmf-wxx","display":"Google Meet"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2057 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 08:11:38] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T08:11:38.341135Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2046 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2057 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 08:17:17] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T08:17:17.427011Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":17,"status":"dispatching"} 
[2025-07-24 08:17:19] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2047 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T08:17:19.531199Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2092.0,"user_id":84,"entity_id":17,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T08:17:17.439296Z","data":{"event_id":7,"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"16:30","selected_location":{"type":"zoom","value":"https://meet.google.com/inw-yfmf-wxx","display":"Zoom"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"updated_at":"2025-07-24T08:17:17.000000Z","created_at":"2025-07-24T08:17:17.000000Z","id":17,"form_data":{"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"16:30","selected_location":{"type":"zoom","value":"https://meet.google.com/inw-yfmf-wxx","display":"Zoom"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2047 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 08:17:21] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2064 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T08:17:21.599128Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2067.0,"user_id":84,"entity_id":17,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T08:17:19.532507Z","data":{"event_id":7,"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"16:30","selected_location":{"type":"zoom","value":"https://meet.google.com/inw-yfmf-wxx","display":"Zoom"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"updated_at":"2025-07-24T08:17:17.000000Z","created_at":"2025-07-24T08:17:17.000000Z","id":17,"form_data":{"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"16:30","selected_location":{"type":"zoom","value":"https://meet.google.com/inw-yfmf-wxx","display":"Zoom"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2064 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 08:17:21] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T08:17:21.599996Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2047 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2064 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 08:23:06] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T08:23:06.931806Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":18,"status":"dispatching"} 
[2025-07-24 08:23:09] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2058 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T08:23:09.062239Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2114.0,"user_id":84,"entity_id":18,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T08:23:06.948326Z","data":{"event_id":7,"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"16:00","selected_location":{"type":"meet","value":"https://meet.google.com/inw-yfmf-wxx","display":"Google Meet"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"updated_at":"2025-07-24T08:23:06.000000Z","created_at":"2025-07-24T08:23:06.000000Z","id":18,"form_data":{"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"16:00","selected_location":{"type":"meet","value":"https://meet.google.com/inw-yfmf-wxx","display":"Google Meet"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2058 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 08:23:11] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T08:23:11.101516Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2039.0,"user_id":84,"entity_id":18,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T08:23:09.062840Z","data":{"event_id":7,"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"16:00","selected_location":{"type":"meet","value":"https://meet.google.com/inw-yfmf-wxx","display":"Google Meet"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"updated_at":"2025-07-24T08:23:06.000000Z","created_at":"2025-07-24T08:23:06.000000Z","id":18,"form_data":{"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"16:00","selected_location":{"type":"meet","value":"https://meet.google.com/inw-yfmf-wxx","display":"Google Meet"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 08:23:11] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T08:23:11.102838Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2058 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 08:24:40] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T08:24:40.630060Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":19,"status":"dispatching"} 
[2025-07-24 08:24:42] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T08:24:42.705408Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2063.0,"user_id":84,"entity_id":19,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T08:24:40.642725Z","data":{"event_id":7,"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"12:30","selected_location":{"type":"in_person","value":"Siliguri West Bengal","display":"In-person meeting"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"updated_at":"2025-07-24T08:24:40.000000Z","created_at":"2025-07-24T08:24:40.000000Z","id":19,"form_data":{"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"12:30","selected_location":{"type":"in_person","value":"Siliguri West Bengal","display":"In-person meeting"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 08:24:44] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2038 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T08:24:44.746518Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2040.0,"user_id":84,"entity_id":19,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T08:24:42.706161Z","data":{"event_id":7,"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"12:30","selected_location":{"type":"in_person","value":"Siliguri West Bengal","display":"In-person meeting"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"updated_at":"2025-07-24T08:24:40.000000Z","created_at":"2025-07-24T08:24:40.000000Z","id":19,"form_data":{"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"12:30","selected_location":{"type":"in_person","value":"Siliguri West Bengal","display":"In-person meeting"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2038 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 08:24:44] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T08:24:44.747814Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2038 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 08:25:26] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T08:25:26.011710Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":20,"status":"dispatching"} 
[2025-07-24 08:25:28] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2018 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T08:25:28.076750Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2055.0,"user_id":84,"entity_id":20,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T08:25:26.021320Z","data":{"event_id":7,"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"12:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"updated_at":"2025-07-24T08:25:25.000000Z","created_at":"2025-07-24T08:25:25.000000Z","id":20,"form_data":{"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"12:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2018 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 08:25:30] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2025 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T08:25:30.104167Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2027.0,"user_id":84,"entity_id":20,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T08:25:28.077438Z","data":{"event_id":7,"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"12:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"updated_at":"2025-07-24T08:25:25.000000Z","created_at":"2025-07-24T08:25:25.000000Z","id":20,"form_data":{"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-24","time":"12:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2025 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 08:25:30] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T08:25:30.106045Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2018 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2025 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 08:36:04] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T08:36:04.289934Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":21,"status":"dispatching"} 
[2025-07-24 08:36:06] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T08:36:06.493884Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2150.0,"user_id":84,"entity_id":21,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T08:36:04.344386Z","data":{"event_id":7,"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-25","time":"15:30","selected_location":{"type":"meet","value":"https://meet.google.com/inw-yfmf-wxx","display":"Google Meet"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"updated_at":"2025-07-24T08:36:04.000000Z","created_at":"2025-07-24T08:36:04.000000Z","id":21,"form_data":{"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-25","time":"15:30","selected_location":{"type":"meet","value":"https://meet.google.com/inw-yfmf-wxx","display":"Google Meet"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 08:36:08] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T08:36:08.526873Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2032.0,"user_id":84,"entity_id":21,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T08:36:06.494886Z","data":{"event_id":7,"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-25","time":"15:30","selected_location":{"type":"meet","value":"https://meet.google.com/inw-yfmf-wxx","display":"Google Meet"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"updated_at":"2025-07-24T08:36:04.000000Z","created_at":"2025-07-24T08:36:04.000000Z","id":21,"form_data":{"name":"Deep Sharkar","email":"<EMAIL>","phone":"8695632012","date":"2025-07-25","time":"15:30","selected_location":{"type":"meet","value":"https://meet.google.com/inw-yfmf-wxx","display":"Google Meet"},"custom_fields":["opportunity_name"],"custom_fields_value":["my new well"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 08:36:08] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T08:36:08.527464Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 09:51:06] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T09:51:06.806210Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":22,"status":"dispatching"} 
[2025-07-24 09:51:08] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2042 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T09:51:08.927791Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2111.0,"user_id":84,"entity_id":22,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T09:51:06.816584Z","data":{"event_id":7,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"16:30","selected_location":{"type":"zoom","value":"https://meet.google.com/inw-yfmf-wxx","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T09:51:06.000000Z","created_at":"2025-07-24T09:51:06.000000Z","id":22,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"16:30","selected_location":{"type":"zoom","value":"https://meet.google.com/inw-yfmf-wxx","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2042 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 09:51:10] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2015 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T09:51:10.946255Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2017.0,"user_id":84,"entity_id":22,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T09:51:08.929379Z","data":{"event_id":7,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"16:30","selected_location":{"type":"zoom","value":"https://meet.google.com/inw-yfmf-wxx","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T09:51:06.000000Z","created_at":"2025-07-24T09:51:06.000000Z","id":22,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"16:30","selected_location":{"type":"zoom","value":"https://meet.google.com/inw-yfmf-wxx","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2015 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 09:51:10] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T09:51:10.947088Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2042 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2015 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 09:51:51] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T09:51:51.209656Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":23,"status":"dispatching"} 
[2025-07-24 09:51:53] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2040 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T09:51:53.299112Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2079.0,"user_id":84,"entity_id":23,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T09:51:51.219620Z","data":{"event_id":7,"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-26","time":"14:30","selected_location":{"type":"zoom","value":"https://meet.google.com/inw-yfmf-wxx","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T09:51:51.000000Z","created_at":"2025-07-24T09:51:51.000000Z","id":23,"form_data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-26","time":"14:30","selected_location":{"type":"zoom","value":"https://meet.google.com/inw-yfmf-wxx","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2040 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 09:51:55] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T09:51:55.346886Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2047.0,"user_id":84,"entity_id":23,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T09:51:53.300323Z","data":{"event_id":7,"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-26","time":"14:30","selected_location":{"type":"zoom","value":"https://meet.google.com/inw-yfmf-wxx","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T09:51:51.000000Z","created_at":"2025-07-24T09:51:51.000000Z","id":23,"form_data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-26","time":"14:30","selected_location":{"type":"zoom","value":"https://meet.google.com/inw-yfmf-wxx","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 09:51:55] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T09:51:55.348321Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2040 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 10:18:13] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-24T10:18:13.468838Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-24 10:18:15] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T10:18:15.592903Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2114.0,"user_id":84,"entity_id":8,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T10:18:13.478626Z","data":{"id":8,"title":"OMX Flow AI BOOk","start_date":"2025-07-24 15:49:00","end_date":"2025-07-31 15:50:00","duration":60,"booking_per_slot":1,"minimum_notice":0,"description":"The Bot Flow Builder is a visual tool that allows you to design, customise, and automate chatbot conversations with a simple drag-and-drop interface.","location":"in_person","meet_link":"https://meet.google.com/mcc-dzqz-qzm","physical_address":"Siliguri West Bengal","custom_fields":[{"type":"full_name","label":"Full Name"}],"date_override":null,"created_by":84,"slots_created":64,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 10:18:17] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2019 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T10:18:17.615348Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2022.0,"user_id":84,"entity_id":8,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T10:18:15.593675Z","data":{"id":8,"title":"OMX Flow AI BOOk","start_date":"2025-07-24 15:49:00","end_date":"2025-07-31 15:50:00","duration":60,"booking_per_slot":1,"minimum_notice":0,"description":"The Bot Flow Builder is a visual tool that allows you to design, customise, and automate chatbot conversations with a simple drag-and-drop interface.","location":"in_person","meet_link":"https://meet.google.com/mcc-dzqz-qzm","physical_address":"Siliguri West Bengal","custom_fields":[{"type":"full_name","label":"Full Name"}],"date_override":null,"created_by":84,"slots_created":64,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2019 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 10:18:17] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 2 {"timestamp":"2025-07-24T10:18:17.616582Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2019 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 10:19:26] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T10:19:26.447850Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":24,"status":"dispatching"} 
[2025-07-24 10:19:28] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T10:19:28.547239Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2085.0,"user_id":84,"entity_id":24,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T10:19:26.462478Z","data":{"event_id":8,"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-25","time":"13:30","selected_location":{"type":"zoom","value":"https://meet.google.com/mcc-dzqz-qzm","display":"Zoom"},"custom_fields":["full_name"],"custom_fields_value":["Parichay Singha Parichay Singha"],"updated_at":"2025-07-24T10:19:26.000000Z","created_at":"2025-07-24T10:19:26.000000Z","id":24,"form_data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-25","time":"13:30","selected_location":{"type":"zoom","value":"https://meet.google.com/mcc-dzqz-qzm","display":"Zoom"},"custom_fields":["full_name"],"custom_fields_value":["Parichay Singha Parichay Singha"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 10:19:30] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T10:19:30.581425Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2033.0,"user_id":84,"entity_id":24,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T10:19:28.548393Z","data":{"event_id":8,"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-25","time":"13:30","selected_location":{"type":"zoom","value":"https://meet.google.com/mcc-dzqz-qzm","display":"Zoom"},"custom_fields":["full_name"],"custom_fields_value":["Parichay Singha Parichay Singha"],"updated_at":"2025-07-24T10:19:26.000000Z","created_at":"2025-07-24T10:19:26.000000Z","id":24,"form_data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-25","time":"13:30","selected_location":{"type":"zoom","value":"https://meet.google.com/mcc-dzqz-qzm","display":"Zoom"},"custom_fields":["full_name"],"custom_fields_value":["Parichay Singha Parichay Singha"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 10:19:30] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T10:19:30.583139Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 10:22:33] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-24T10:22:33.805117Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-24 10:22:35] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T10:22:35.915826Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2105.0,"user_id":84,"entity_id":9,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T10:22:33.810712Z","data":{"id":9,"title":"MY Flow Bot Booking Love","start_date":"2025-07-24 15:51:00","end_date":"2025-07-31 15:51:00","duration":60,"booking_per_slot":1,"minimum_notice":60,"description":"The Bot Flow Builder is a visual tool that allows you to design, customize, and automate chatbot conversations with a simple drag-and-drop interface.","location":"zoom","meet_link":"https://meet.google.com/upv-fecq-zja","physical_address":null,"custom_fields":null,"date_override":null,"created_by":84,"slots_created":64,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 10:22:37] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T10:22:37.949789Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2032.0,"user_id":84,"entity_id":9,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T10:22:35.917433Z","data":{"id":9,"title":"MY Flow Bot Booking Love","start_date":"2025-07-24 15:51:00","end_date":"2025-07-31 15:51:00","duration":60,"booking_per_slot":1,"minimum_notice":60,"description":"The Bot Flow Builder is a visual tool that allows you to design, customize, and automate chatbot conversations with a simple drag-and-drop interface.","location":"zoom","meet_link":"https://meet.google.com/upv-fecq-zja","physical_address":null,"custom_fields":null,"date_override":null,"created_by":84,"slots_created":64,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 10:22:37] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 2 {"timestamp":"2025-07-24T10:22:37.950579Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 10:23:15] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T10:23:15.064931Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":25,"status":"dispatching"} 
[2025-07-24 10:23:17] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T10:23:17.211381Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2126.0,"user_id":84,"entity_id":25,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T10:23:15.085458Z","data":{"event_id":9,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"15:30","selected_location":{"type":"zoom","value":"https://meet.google.com/upv-fecq-zja","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T10:23:15.000000Z","created_at":"2025-07-24T10:23:15.000000Z","id":25,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"15:30","selected_location":{"type":"zoom","value":"https://meet.google.com/upv-fecq-zja","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 10:23:19] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T10:23:19.243922Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2030.0,"user_id":84,"entity_id":25,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T10:23:17.213725Z","data":{"event_id":9,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"15:30","selected_location":{"type":"zoom","value":"https://meet.google.com/upv-fecq-zja","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T10:23:15.000000Z","created_at":"2025-07-24T10:23:15.000000Z","id":25,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"15:30","selected_location":{"type":"zoom","value":"https://meet.google.com/upv-fecq-zja","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 10:23:19] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T10:23:19.244750Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 11:00:30] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T11:00:30.494747Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":26,"status":"dispatching"} 
[2025-07-24 11:00:32] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T11:00:32.692453Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2166.0,"user_id":84,"entity_id":26,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T11:00:30.526456Z","data":{"event_id":9,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"13:30","selected_location":{"type":"zoom","value":"https://meet.google.com/upv-fecq-zja","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T11:00:30.000000Z","created_at":"2025-07-24T11:00:30.000000Z","id":26,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"13:30","selected_location":{"type":"zoom","value":"https://meet.google.com/upv-fecq-zja","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 11:00:34] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T11:00:34.729986Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2036.0,"user_id":84,"entity_id":26,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T11:00:32.694030Z","data":{"event_id":9,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"13:30","selected_location":{"type":"zoom","value":"https://meet.google.com/upv-fecq-zja","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T11:00:30.000000Z","created_at":"2025-07-24T11:00:30.000000Z","id":26,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"13:30","selected_location":{"type":"zoom","value":"https://meet.google.com/upv-fecq-zja","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 11:00:34] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T11:00:34.731468Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 11:01:36] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T11:01:36.292586Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":27,"status":"dispatching"} 
[2025-07-24 11:01:38] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2036 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T11:01:38.414078Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2102.0,"user_id":84,"entity_id":27,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T11:01:36.311889Z","data":{"event_id":9,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"14:00","selected_location":{"type":"zoom","value":"https://meet.google.com/upv-fecq-zja","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T11:01:36.000000Z","created_at":"2025-07-24T11:01:36.000000Z","id":27,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"14:00","selected_location":{"type":"zoom","value":"https://meet.google.com/upv-fecq-zja","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2036 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 11:01:40] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T11:01:40.461463Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2044.0,"user_id":84,"entity_id":27,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T11:01:38.417038Z","data":{"event_id":9,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"14:00","selected_location":{"type":"zoom","value":"https://meet.google.com/upv-fecq-zja","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T11:01:36.000000Z","created_at":"2025-07-24T11:01:36.000000Z","id":27,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-24","time":"14:00","selected_location":{"type":"zoom","value":"https://meet.google.com/upv-fecq-zja","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 11:01:40] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T11:01:40.462756Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2036 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 11:23:54] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-24T11:23:54.282224Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-24 11:23:56] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T11:23:56.392391Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2104.0,"user_id":84,"entity_id":10,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T11:23:54.288701Z","data":{"id":10,"title":"AI Bot Flow Live","start_date":"2025-07-24 11:23:37","end_date":"2026-07-24 11:23:37","duration":60,"booking_per_slot":1,"minimum_notice":60,"description":"OK","location":"in_person","meet_link":null,"physical_address":"Siliguri","custom_fields":null,"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 11:23:58] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2052 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T11:23:58.449420Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2056.0,"user_id":84,"entity_id":10,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T11:23:56.393716Z","data":{"id":10,"title":"AI Bot Flow Live","start_date":"2025-07-24 11:23:37","end_date":"2026-07-24 11:23:37","duration":60,"booking_per_slot":1,"minimum_notice":60,"description":"OK","location":"in_person","meet_link":null,"physical_address":"Siliguri","custom_fields":null,"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2052 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 11:23:58] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 2 {"timestamp":"2025-07-24T11:23:58.450711Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2052 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 11:24:28] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T11:24:28.713932Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":28,"status":"dispatching"} 
[2025-07-24 11:24:30] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2021 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T11:24:30.780745Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2060.0,"user_id":84,"entity_id":28,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T11:24:28.720329Z","data":{"event_id":10,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"13:30","selected_location":{"type":"in_person","value":"Siliguri","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T11:24:28.000000Z","created_at":"2025-07-24T11:24:28.000000Z","id":28,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"13:30","selected_location":{"type":"in_person","value":"Siliguri","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2021 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 11:24:32] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2008 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T11:24:32.792209Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2011.0,"user_id":84,"entity_id":28,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T11:24:30.781495Z","data":{"event_id":10,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"13:30","selected_location":{"type":"in_person","value":"Siliguri","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T11:24:28.000000Z","created_at":"2025-07-24T11:24:28.000000Z","id":28,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"13:30","selected_location":{"type":"in_person","value":"Siliguri","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2008 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 11:24:32] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T11:24:32.794259Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2021 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2008 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 12:02:06] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-24T12:02:06.198376Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-24 12:02:10] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2038 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:02:10.134077Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":3927.0,"user_id":84,"entity_id":11,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T12:02:06.206635Z","data":{"id":11,"title":"OK Live","start_date":"2025-07-24 12:01:47","end_date":"2026-07-24 12:01:47","duration":60,"booking_per_slot":1,"minimum_notice":15,"description":"ok","location":"in_person","meet_link":null,"physical_address":"Siliguri","custom_fields":[{"type":"opportunity_name","label":"Opportunity Name"}],"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2038 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:02:12] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:02:12.176023Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2041.0,"user_id":84,"entity_id":11,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T12:02:10.134634Z","data":{"id":11,"title":"OK Live","start_date":"2025-07-24 12:01:47","end_date":"2026-07-24 12:01:47","duration":60,"booking_per_slot":1,"minimum_notice":15,"description":"ok","location":"in_person","meet_link":null,"physical_address":"Siliguri","custom_fields":[{"type":"opportunity_name","label":"Opportunity Name"}],"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:02:12] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 2 {"timestamp":"2025-07-24T12:02:12.177570Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2038 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 12:03:59] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-24T12:03:59.364800Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-24 12:04:01] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2031 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:04:01.421940Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2054.0,"user_id":84,"entity_id":12,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T12:03:59.367577Z","data":{"id":12,"title":"my live dot net","start_date":"2025-07-24 12:03:47","end_date":"2026-07-24 12:03:47","duration":60,"booking_per_slot":1,"minimum_notice":45,"description":"ok","location":"meet","meet_link":"https://meet.google.com/med-syda-tdi","physical_address":null,"custom_fields":[{"type":"city","label":"City"}],"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2031 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:04:03] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:04:03.448600Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2026.0,"user_id":84,"entity_id":12,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T12:04:01.422602Z","data":{"id":12,"title":"my live dot net","start_date":"2025-07-24 12:03:47","end_date":"2026-07-24 12:03:47","duration":60,"booking_per_slot":1,"minimum_notice":45,"description":"ok","location":"meet","meet_link":"https://meet.google.com/med-syda-tdi","physical_address":null,"custom_fields":[{"type":"city","label":"City"}],"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:04:03] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 2 {"timestamp":"2025-07-24T12:04:03.449482Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2031 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 12:06:43] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-24T12:06:43.566633Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-24 12:06:45] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:06:45.635131Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2064.0,"user_id":84,"entity_id":13,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T12:06:43.570954Z","data":{"id":13,"title":"New Flow Run","start_date":"2025-07-24 12:06:27","end_date":"2026-07-24 12:06:27","duration":60,"booking_per_slot":1,"minimum_notice":44,"description":"OK this is .","location":"in_person","meet_link":null,"physical_address":"OMx siliguri","custom_fields":null,"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:06:47] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:06:47.670711Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2034.0,"user_id":84,"entity_id":13,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T12:06:45.636253Z","data":{"id":13,"title":"New Flow Run","start_date":"2025-07-24 12:06:27","end_date":"2026-07-24 12:06:27","duration":60,"booking_per_slot":1,"minimum_notice":44,"description":"OK this is .","location":"in_person","meet_link":null,"physical_address":"OMx siliguri","custom_fields":null,"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:06:47] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 2 {"timestamp":"2025-07-24T12:06:47.672078Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 12:09:33] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-24T12:09:33.813613Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-24 12:09:35] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:09:35.896521Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2080.0,"user_id":84,"entity_id":14,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T12:09:33.816591Z","data":{"id":14,"title":"my one flow","start_date":"2025-07-24 12:09:18","end_date":"2026-07-24 12:09:18","duration":60,"booking_per_slot":1,"minimum_notice":42,"description":"this ok.","location":"in_person","meet_link":"https://meet.google.com/med-syda-tdi","physical_address":"ok","custom_fields":null,"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:09:37] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2025 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:09:37.926891Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2029.0,"user_id":84,"entity_id":14,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T12:09:35.897688Z","data":{"id":14,"title":"my one flow","start_date":"2025-07-24 12:09:18","end_date":"2026-07-24 12:09:18","duration":60,"booking_per_slot":1,"minimum_notice":42,"description":"this ok.","location":"in_person","meet_link":"https://meet.google.com/med-syda-tdi","physical_address":"ok","custom_fields":null,"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2025 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:09:37] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 2 {"timestamp":"2025-07-24T12:09:37.927906Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2025 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 12:21:40] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-24T12:21:40.145413Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-24 12:21:42] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:21:42.247412Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2097.0,"user_id":84,"entity_id":15,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T12:21:40.150643Z","data":{"id":15,"title":"ok done","start_date":"2025-07-24 12:21:21","end_date":"2026-07-24 12:21:21","duration":60,"booking_per_slot":1,"minimum_notice":15,"description":"ok","location":"zoom","meet_link":"https://meet.google.com/med-syda-tdi","physical_address":null,"custom_fields":null,"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:21:44] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2043 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:21:44.294769Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2046.0,"user_id":84,"entity_id":15,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T12:21:42.248803Z","data":{"id":15,"title":"ok done","start_date":"2025-07-24 12:21:21","end_date":"2026-07-24 12:21:21","duration":60,"booking_per_slot":1,"minimum_notice":15,"description":"ok","location":"zoom","meet_link":"https://meet.google.com/med-syda-tdi","physical_address":null,"custom_fields":null,"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2043 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:21:44] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 2 {"timestamp":"2025-07-24T12:21:44.297038Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2043 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 12:29:04] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-24T12:29:04.010690Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-24 12:29:06] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2048 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:29:06.126241Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2111.0,"user_id":84,"entity_id":16,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T12:29:04.015333Z","data":{"id":16,"title":"Date Range\tDuration","start_date":"2025-07-24 12:28:51","end_date":"2026-07-24 12:28:51","duration":60,"booking_per_slot":1,"minimum_notice":43,"description":"ok","location":"phone","meet_link":"8617555736","physical_address":null,"custom_fields":null,"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2048 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:29:08] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:29:08.174854Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2048.0,"user_id":84,"entity_id":16,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-24T12:29:06.127134Z","data":{"id":16,"title":"Date Range\tDuration","start_date":"2025-07-24 12:28:51","end_date":"2026-07-24 12:28:51","duration":60,"booking_per_slot":1,"minimum_notice":43,"description":"ok","location":"phone","meet_link":"8617555736","physical_address":null,"custom_fields":null,"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:29:08] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 2 {"timestamp":"2025-07-24T12:29:08.176420Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2048 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 12:29:42] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T12:29:42.411106Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":29,"status":"dispatching"} 
[2025-07-24 12:29:44] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:29:44.498965Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2080.0,"user_id":84,"entity_id":29,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T12:29:42.418811Z","data":{"event_id":16,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"15:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T12:29:42.000000Z","created_at":"2025-07-24T12:29:42.000000Z","id":29,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"15:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:29:46] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:29:46.526370Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2026.0,"user_id":84,"entity_id":29,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T12:29:44.500027Z","data":{"event_id":16,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"15:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T12:29:42.000000Z","created_at":"2025-07-24T12:29:42.000000Z","id":29,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"15:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:29:46] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T12:29:46.527144Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2044 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 12:31:27] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T12:31:27.228244Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":30,"status":"dispatching"} 
[2025-07-24 12:31:29] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2053 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:31:29.357495Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2116.0,"user_id":84,"entity_id":30,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T12:31:27.241224Z","data":{"event_id":16,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"14:30","selected_location":{"type":"in_person","value":"Siligurii West bengal","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T12:31:27.000000Z","created_at":"2025-07-24T12:31:27.000000Z","id":30,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"14:30","selected_location":{"type":"in_person","value":"Siligurii West bengal","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2053 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:31:31] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2035 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:31:31.395330Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2037.0,"user_id":84,"entity_id":30,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T12:31:29.357936Z","data":{"event_id":16,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"14:30","selected_location":{"type":"in_person","value":"Siligurii West bengal","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-24T12:31:27.000000Z","created_at":"2025-07-24T12:31:27.000000Z","id":30,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"14:30","selected_location":{"type":"in_person","value":"Siligurii West bengal","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2035 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:31:31] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T12:31:31.397656Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2053 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2035 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 12:46:05] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T12:46:05.518103Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":31,"status":"dispatching"} 
[2025-07-24 12:46:07] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:46:07.647892Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2113.0,"user_id":84,"entity_id":31,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T12:46:05.534845Z","data":{"event_id":3,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-25","time":"13:00","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["postal_code"],"custom_fields_value":["734429"],"updated_at":"2025-07-24T12:46:05.000000Z","created_at":"2025-07-24T12:46:05.000000Z","id":31,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-25","time":"13:00","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["postal_code"],"custom_fields_value":["734429"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:46:09] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2031 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:46:09.683428Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2034.0,"user_id":84,"entity_id":31,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T12:46:07.648882Z","data":{"event_id":3,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-25","time":"13:00","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["postal_code"],"custom_fields_value":["734429"],"updated_at":"2025-07-24T12:46:05.000000Z","created_at":"2025-07-24T12:46:05.000000Z","id":31,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-25","time":"13:00","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["postal_code"],"custom_fields_value":["734429"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2031 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:46:09] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T12:46:09.686014Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2031 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 12:46:55] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T12:46:55.534699Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":32,"status":"dispatching"} 
[2025-07-24 12:46:57] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:46:57.609529Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2068.0,"user_id":84,"entity_id":32,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T12:46:55.541362Z","data":{"event_id":3,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"in_person","display":"In Person","value":"Siliguri Westbengal"},"custom_fields":["postal_code","full_name","date_of_birth","city"],"custom_fields_value":["734429","wafjkhjl","2025-07-26","siliguri"],"updated_at":"2025-07-24T12:46:55.000000Z","created_at":"2025-07-24T12:46:55.000000Z","id":32,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"in_person","display":"In Person","value":"Siliguri Westbengal"},"custom_fields":["postal_code","full_name","date_of_birth","city"],"custom_fields_value":["734429","wafjkhjl","2025-07-26","siliguri"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:46:59] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T12:46:59.648574Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2038.0,"user_id":84,"entity_id":32,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T12:46:57.610790Z","data":{"event_id":3,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"in_person","display":"In Person","value":"Siliguri Westbengal"},"custom_fields":["postal_code","full_name","date_of_birth","city"],"custom_fields_value":["734429","wafjkhjl","2025-07-26","siliguri"],"updated_at":"2025-07-24T12:46:55.000000Z","created_at":"2025-07-24T12:46:55.000000Z","id":32,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"in_person","display":"In Person","value":"Siliguri Westbengal"},"custom_fields":["postal_code","full_name","date_of_birth","city"],"custom_fields_value":["734429","wafjkhjl","2025-07-26","siliguri"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 12:46:59] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T12:46:59.649073Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-24 15:52:04] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-24T15:52:04.408200Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":33,"status":"dispatching"} 
[2025-07-24 15:52:11] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T15:52:11.728947Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":7187.0,"user_id":84,"entity_id":33,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T15:52:04.542108Z","data":{"event_id":16,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"zoom","value":"https://meet.google.com/med-syda-tdi","display":"Zoom"},"custom_fields":["date_of_birth","opportunity_name","city"],"custom_fields_value":["2025-07-03","my new well","string:Siliguri"],"updated_at":"2025-07-24T15:52:03.000000Z","created_at":"2025-07-24T15:52:03.000000Z","id":33,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"zoom","value":"https://meet.google.com/med-syda-tdi","display":"Zoom"},"custom_fields":["date_of_birth","opportunity_name","city"],"custom_fields_value":["2025-07-03","my new well","string:Siliguri"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 15:52:13] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2056 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-24T15:52:13.790451Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2060.0,"user_id":84,"entity_id":33,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-24T15:52:11.730907Z","data":{"event_id":16,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"zoom","value":"https://meet.google.com/med-syda-tdi","display":"Zoom"},"custom_fields":["date_of_birth","opportunity_name","city"],"custom_fields_value":["2025-07-03","my new well","string:Siliguri"],"updated_at":"2025-07-24T15:52:03.000000Z","created_at":"2025-07-24T15:52:03.000000Z","id":33,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"zoom","value":"https://meet.google.com/med-syda-tdi","display":"Zoom"},"custom_fields":["date_of_birth","opportunity_name","city"],"custom_fields_value":["2025-07-03","my new well","string:Siliguri"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2056 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-24 15:52:13] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-24T15:52:13.791745Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2056 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
