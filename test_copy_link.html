<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Copy Event Link</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        button { padding: 10px 20px; margin: 10px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-outline-primary { background: white; color: #007bff; border: 1px solid #007bff; }
        .toast { position: fixed; top: 20px; right: 20px; background: #28a745; color: white; padding: 15px; border-radius: 5px; display: none; }
        .log { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Copy Event Link Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Basic Copy Functionality</h3>
        <button id="test-copy-btn" class="btn-outline-primary" onclick="testCopyFunction(this)">
            <i class="ti ti-copy"></i> Copy Test Link
        </button>
        <div id="test-log-1" class="log"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Modal Simulation</h3>
        <div id="viewEventModal" data-event-id="123" style="display: none;">Mock Modal</div>
        <button id="copy-event-link-btn" class="btn-outline-primary" onclick="copyEventLink(this)">
            <i class="ti ti-copy"></i> Copy Event Link
        </button>
        <div id="test-log-2" class="log"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Clipboard API Check</h3>
        <button onclick="checkClipboardAPI()" class="btn-primary">Check Clipboard API</button>
        <div id="test-log-3" class="log"></div>
    </div>

    <!-- Toast notification -->
    <div id="copy-event-toast" class="toast">
        <span id="copy-event-toast-message">Event link copied!</span>
        <button onclick="hideCopyEventToast()" style="background: none; border: none; color: white; margin-left: 10px;">×</button>
    </div>

    <script>
        // Test basic copy functionality
        function testCopyFunction(btn) {
            const testLink = 'https://example.com/test-event/123';
            log('test-log-1', 'Testing copy functionality...');
            log('test-log-1', 'Link to copy: ' + testLink);
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(testLink).then(function() {
                    log('test-log-1', 'SUCCESS: Link copied using modern API');
                    updateButton(btn, true);
                }).catch(function(err) {
                    log('test-log-1', 'ERROR: ' + err);
                    updateButton(btn, false);
                });
            } else {
                log('test-log-1', 'Clipboard API not available, trying fallback...');
                fallbackCopy(testLink, btn);
            }
        }

        // Copy event link function (simplified version)
        function copyEventLink(btn) {
            log('test-log-2', 'copyEventLink called');
            
            const eventId = $('#viewEventModal').data('event-id');
            log('test-log-2', 'Event ID: ' + eventId);
            
            if (!eventId) {
                log('test-log-2', 'ERROR: No event ID found');
                showCopyEventToast('No event selected');
                return;
            }
            
            const eventLink = window.location.origin + '/calendar-events/' + eventId;
            log('test-log-2', 'Event link: ' + eventLink);
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(eventLink).then(function() {
                    log('test-log-2', 'SUCCESS: Event link copied');
                    showCopyEventToast('Event link copied!');
                    updateButton(btn, true);
                }).catch(function(err) {
                    log('test-log-2', 'ERROR: ' + err);
                    showCopyEventToast('Failed to copy link');
                    updateButton(btn, false);
                });
            } else {
                log('test-log-2', 'Using fallback copy method');
                fallbackCopy(eventLink, btn);
            }
        }

        // Check clipboard API availability
        function checkClipboardAPI() {
            log('test-log-3', 'Checking clipboard API...');
            log('test-log-3', 'navigator.clipboard exists: ' + (!!navigator.clipboard));
            log('test-log-3', 'HTTPS/localhost: ' + (location.protocol === 'https:' || location.hostname === 'localhost'));
            log('test-log-3', 'Current protocol: ' + location.protocol);
            log('test-log-3', 'Current hostname: ' + location.hostname);
            
            if (navigator.clipboard) {
                log('test-log-3', 'Clipboard API is available');
            } else {
                log('test-log-3', 'Clipboard API is NOT available - will use fallback');
            }
        }

        // Fallback copy method
        function fallbackCopy(text, btn) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.top = "0";
            textArea.style.left = "0";
            
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    log('test-log-2', 'SUCCESS: Fallback copy worked');
                    showCopyEventToast('Event link copied!');
                    updateButton(btn, true);
                } else {
                    log('test-log-2', 'ERROR: Fallback copy failed');
                    showCopyEventToast('Failed to copy link');
                    updateButton(btn, false);
                }
            } catch (err) {
                log('test-log-2', 'ERROR: Fallback copy exception: ' + err);
                showCopyEventToast('Failed to copy link');
                updateButton(btn, false);
            }
            
            document.body.removeChild(textArea);
        }

        // Update button appearance
        function updateButton(btn, success) {
            const $btn = $(btn);
            const originalHtml = $btn.html();
            
            if (success) {
                $btn.html('<i class="ti ti-check"></i> Copied!')
                    .removeClass('btn-outline-primary')
                    .addClass('btn-success');
            } else {
                $btn.html('<i class="ti ti-x"></i> Failed')
                    .removeClass('btn-outline-primary')
                    .addClass('btn-danger');
            }
            
            setTimeout(function() {
                $btn.html(originalHtml)
                    .removeClass('btn-success btn-danger')
                    .addClass('btn-outline-primary');
            }, 3000);
        }

        // Show toast notification
        function showCopyEventToast(message) {
            const toast = document.getElementById('copy-event-toast');
            const msg = document.getElementById('copy-event-toast-message');
            if (msg) msg.textContent = message;
            if (toast) {
                toast.style.display = 'block';
                setTimeout(hideCopyEventToast, 3000);
            }
        }

        // Hide toast notification
        function hideCopyEventToast() {
            const toast = document.getElementById('copy-event-toast');
            if (toast) {
                toast.style.display = 'none';
            }
        }

        // Logging function
        function log(elementId, message) {
            const logElement = document.getElementById(elementId);
            if (logElement) {
                logElement.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            }
        }

        // Run initial checks
        $(document).ready(function() {
            log('test-log-1', 'Page loaded, jQuery ready');
            log('test-log-2', 'Modal element exists: ' + ($('#viewEventModal').length > 0));
            log('test-log-3', 'Ready to test clipboard functionality');
        });
    </script>
</body>
</html>
